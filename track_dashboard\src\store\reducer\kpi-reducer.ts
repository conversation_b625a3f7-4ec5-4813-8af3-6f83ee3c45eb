import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import { defineds } from "../../pages/dashboard/utils/default-ranges";
import { format, startOfDay, endOfDay } from "date-fns";

export interface KPIStoreRange {
  end: string;
  start: string;
}
interface KPIState {
  dateRange: KPIStoreRange;
}

const defaultStart = startOfDay(defineds.startOfToday);
const defaultEnd = endOfDay(defineds.endOfToday);

export const initialKPIState: KPIState = {
  dateRange: {
    start: format(defaultStart, "yyyy-MM-dd'T'HH:mm:ss"),
    end: format(defaultEnd, "yyyy-MM-dd'T'HH:mm:ss"),
  },
};

const kpiSlice = createSlice({
  name: "kpi",
  initialState: initialKPIState,
  reducers: {
    setdateRange: (state: KPIState, action: PayloadAction<KPIStoreRange>) => {
      state.dateRange = action.payload;
    },
  },
});

export const { setdateRange } = kpiSlice.actions;

export default kpiSlice.reducer;
