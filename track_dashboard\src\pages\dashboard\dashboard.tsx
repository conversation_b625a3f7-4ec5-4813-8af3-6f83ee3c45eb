import { Box, Heading, Text, SimpleGrid, Card, CardBody, Stat, StatLabel, StatNumber, StatHelpText,Flex } from '@chakra-ui/react';
import { FiUsers, FiTrendingUp, FiDollarSign, FiActivity } from 'react-icons/fi';

import DateRangeSelect from './components/date-select';
import { useAppSelector } from '../../store/store';
const Dashboard = () => {
   
  const { dateRange } = useAppSelector((state) => state.kpi)

  
  console.log('dateRanges====>', dateRange);
    return (
    <Box p={0}>
      <Flex justify="space-between" align="center" mb={6}>
        <Box>
          <Heading size="lg">
            Dashboard
          </Heading>
         
        </Box>
        <DateRangeSelect />
      </Flex>

      {/* Your dashboard metrics */}
      <Box>
        <Text> Charts and metrics filtered by selected date </Text>
      </Box>
    </Box>
  )
};

export default Dashboard;
