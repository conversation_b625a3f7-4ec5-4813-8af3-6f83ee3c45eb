import { Box, Heading, Text, SimpleGrid, Card, CardBody, Stat, StatLabel, StatNumber, StatHelpText,Flex,useToast} from '@chakra-ui/react';

//import { FiUsers, FiTrendingUp, FiDollarSign, FiActivity } from 'react-icons/fi';
import kpiService from '@/api/service/kpi/index';
import { useApiQuery } from '@/hooks/react-query-hooks';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { useAppDispatch } from '../../store/store';
import { useEffect,useState} from 'react';
import DateRangeSelect from './components/date-select';
import { useAppSelector } from '../../store/store';
const Dashboard = () => {
   
  const { dateRange } = useAppSelector((state) => state.kpi)
const toast = useToast();
   const [initialLoadStarted, setInitialLoadStarted] = useState(false);

   const {
      data: aggTrackData,
      isFetching: trackingLoading,
      isLoading: trackingDataLoading,
      errorMessage,
      refetch: getInitialKpiData,
   } = useApiQuery({
      queryKey: ['tracking-data'],
      queryFn: () => kpiService.getTrackData(getPayload()),
      getInitialData: () => undefined,
      enabled: false,
   });

      const getPayload = () => {
      return {
         clientId: LocalStorageService.getItem(Keys.ClientId) as string,
         startDate: dateRange.start,
         endDate: dateRange.end,
      };
   };
   const dispatch= useAppDispatch();

    return (
      <>
               {!initialLoadStarted ? (
            <Loading />
         ) : (<Box p={0}>
      <Flex justify="space-between" align="center" mb={6}>
        <Box>
          <Heading size="lg">
            Dashboard
          </Heading>
         
        </Box>
        <DateRangeSelect />
      </Flex>

      {/* Your dashboard metrics */}
      <Box>
        <Text> Charts and metrics filtered by selected date </Text>
      </Box>
    </Box>)}
      </>
    
  )
};

export default Dashboard;
